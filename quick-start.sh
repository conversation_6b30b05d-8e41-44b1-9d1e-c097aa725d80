#!/bin/bash

# MCP Server Quick Start Script
# This script helps you quickly set up and run an MCP server example

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists java; then
        print_error "Java is not installed. Please install Java 11 or higher."
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1-2)
    if [[ $(echo "$JAVA_VERSION < 11" | bc -l) -eq 1 ]]; then
        print_error "Java 11 or higher is required. Current version: $JAVA_VERSION"
        exit 1
    fi
    
    if ! command_exists mvn; then
        print_error "Maven is not installed. Please install Maven 3.6 or higher."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to build the project
build_project() {
    local project_dir="$1"
    print_status "Building project in $project_dir..."
    
    cd "$project_dir"
    
    # Clean and compile
    mvn clean compile
    
    # Run tests
    print_status "Running tests..."
    mvn test
    
    # Package
    print_status "Creating executable JAR..."
    mvn package
    
    print_success "Build completed successfully"
}

# Function to run the server
run_server() {
    local project_dir="$1"
    local jar_file=$(find "$project_dir/target" -name "*.jar" -not -name "*-sources.jar" | head -n 1)
    
    if [[ ! -f "$jar_file" ]]; then
        print_error "JAR file not found. Please build the project first."
        exit 1
    fi
    
    print_status "Starting MCP server..."
    print_status "JAR file: $jar_file"
    print_warning "Press Ctrl+C to stop the server"
    
    java -jar "$jar_file"
}

# Function to setup Claude Desktop configuration
setup_claude_config() {
    local project_dir="$1"
    local jar_file=$(find "$project_dir/target" -name "*.jar" -not -name "*-sources.jar" | head -n 1)
    local jar_path=$(realpath "$jar_file")
    
    # Determine config file location based on OS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        CONFIG_DIR="$HOME/Library/Application Support/Claude"
    elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
        CONFIG_DIR="$APPDATA/Claude"
    else
        CONFIG_DIR="$HOME/.config/claude"
    fi
    
    CONFIG_FILE="$CONFIG_DIR/claude_desktop_config.json"
    
    print_status "Setting up Claude Desktop configuration..."
    
    # Create config directory if it doesn't exist
    mkdir -p "$CONFIG_DIR"
    
    # Create or update config file
    cat > "$CONFIG_FILE" << EOF
{
  "mcpServers": {
    "example-mcp-server": {
      "command": "java",
      "args": [
        "-jar",
        "$jar_path"
      ],
      "env": {
        "LOG_LEVEL": "INFO"
      }
    }
  }
}
EOF
    
    print_success "Claude Desktop configuration created at: $CONFIG_FILE"
    print_warning "Please restart Claude Desktop to load the new configuration"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [PROJECT]"
    echo ""
    echo "Commands:"
    echo "  build     Build the specified project"
    echo "  run       Run the specified project"
    echo "  setup     Setup Claude Desktop configuration"
    echo "  all       Build, setup, and run (default)"
    echo ""
    echo "Projects:"
    echo "  complete  Complete MCP server example (default)"
    echo "  simple    Simple echo server example"
    echo ""
    echo "Examples:"
    echo "  $0                    # Build and run complete example"
    echo "  $0 build complete     # Build complete example"
    echo "  $0 run complete       # Run complete example"
    echo "  $0 setup complete     # Setup Claude config for complete example"
}

# Main script logic
main() {
    local command="${1:-all}"
    local project="${2:-complete}"
    
    # Map project names to directories
    case "$project" in
        "complete")
            PROJECT_DIR="examples/complete-mcp-server"
            ;;
        "simple")
            PROJECT_DIR="examples/simple-echo-server"
            ;;
        *)
            print_error "Unknown project: $project"
            show_usage
            exit 1
            ;;
    esac
    
    # Check if project directory exists
    if [[ ! -d "$PROJECT_DIR" ]]; then
        print_error "Project directory not found: $PROJECT_DIR"
        exit 1
    fi
    
    # Execute command
    case "$command" in
        "build")
            check_prerequisites
            build_project "$PROJECT_DIR"
            ;;
        "run")
            run_server "$PROJECT_DIR"
            ;;
        "setup")
            setup_claude_config "$PROJECT_DIR"
            ;;
        "all")
            check_prerequisites
            build_project "$PROJECT_DIR"
            setup_claude_config "$PROJECT_DIR"
            print_status "Setup complete! You can now:"
            print_status "1. Restart Claude Desktop"
            print_status "2. Test the MCP server integration"
            print_status "3. Or run the server manually with: $0 run $project"
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
