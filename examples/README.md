# MCP Server Examples

This directory contains complete, working examples of MCP (Model Context Protocol) servers implemented in Java.

## Directory Structure

```
examples/
├── complete-mcp-server/          # Full-featured MCP server example
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/
│   │   │   │   └── com/example/mcp/
│   │   │   └── resources/
│   │   └── test/
│   │       └── java/
│   │           └── com/example/mcp/
│   ├── pom.xml                   # Maven configuration
│   └── README.md                 # Project-specific documentation
├── simple-echo-server/           # Minimal echo server example
├── file-manager-server/          # File management MCP server
└── database-connector-server/    # Database integration example
```

## Getting Started

### Prerequisites

- Java 11 or higher
- Maven 3.6 or higher
- Claude Desktop (for testing integration)

### Building and Running

1. **Navigate to an example directory**:
   ```bash
   cd examples/complete-mcp-server
   ```

2. **Build the project**:
   ```bash
   mvn clean compile
   ```

3. **Run tests**:
   ```bash
   mvn test
   ```

4. **Create executable JAR**:
   ```bash
   mvn package
   ```

5. **Run the server**:
   ```bash
   java -jar target/mcp-server-example-1.0.0.jar
   ```

   Or using Maven:
   ```bash
   mvn exec:java
   ```

### Testing with Claude Desktop

1. **Build the JAR file**:
   ```bash
   mvn package
   ```

2. **Configure Claude Desktop** by editing the configuration file:

   **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
   **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

   ```json
   {
     "mcpServers": {
       "example-mcp-server": {
         "command": "java",
         "args": [
           "-jar",
           "/absolute/path/to/examples/complete-mcp-server/target/mcp-server-example-1.0.0.jar"
         ],
         "env": {
           "LOG_LEVEL": "INFO"
         }
       }
     }
   }
   ```

3. **Restart Claude Desktop** to load the new server configuration.

4. **Test the integration** by asking Claude to use the available tools.

## Example Servers

### 1. Complete MCP Server

**Location**: `complete-mcp-server/`

A full-featured MCP server demonstrating:
- Multiple tool implementations (echo, file operations, calculations)
- Resource management (file access, data retrieval)
- Prompt templates for common tasks
- Comprehensive error handling
- Logging and monitoring
- Unit and integration tests

**Available Tools**:
- `echo`: Simple echo functionality
- `file_read`: Read file contents
- `file_write`: Write content to files
- `calculator`: Basic mathematical operations
- `system_info`: System information retrieval

**Available Resources**:
- File system access via `file://` URIs
- Configuration data
- System metrics

### 2. Simple Echo Server

**Location**: `simple-echo-server/`

A minimal MCP server that demonstrates:
- Basic server setup
- Single tool implementation
- STDIO transport
- Simple error handling

Perfect for learning the fundamentals of MCP server development.

### 3. File Manager Server

**Location**: `file-manager-server/`

Specialized server for file operations:
- File and directory listing
- File content reading and writing
- File metadata retrieval
- Directory operations
- File search capabilities

### 4. Database Connector Server

**Location**: `database-connector-server/`

Database integration example:
- SQL query execution
- Database schema introspection
- Connection pooling
- Transaction management
- Multiple database support (PostgreSQL, MySQL, SQLite)

## Development Workflow

### 1. Create a New MCP Server

```bash
# Copy the complete example as a starting point
cp -r complete-mcp-server my-new-server
cd my-new-server

# Update the Maven coordinates in pom.xml
# Modify the package names and class names
# Implement your custom tools and resources
```

### 2. Add Custom Tools

1. Create a new class implementing the `Tool` interface
2. Register the tool in your server's initialization method
3. Add unit tests for the tool
4. Update documentation

### 3. Add Custom Resources

1. Create a new class implementing the `Resource` interface
2. Register the resource in your server
3. Implement proper URI handling
4. Add access control if needed

### 4. Testing

```bash
# Run unit tests
mvn test

# Run integration tests
mvn verify

# Test with MCP Inspector (if available)
mcp-inspector test target/my-server.jar
```

### 5. Deployment

```bash
# Create standalone JAR
mvn package

# Create native binary (requires GraalVM)
mvn package -Pnative

# Create Docker image
docker build -t my-mcp-server .
```

## Best Practices

1. **Start with the complete example** and modify it for your needs
2. **Follow the naming conventions** used in the examples
3. **Implement comprehensive error handling** for all tools and resources
4. **Add logging** to help with debugging and monitoring
5. **Write tests** for all custom functionality
6. **Document your tools and resources** clearly
7. **Validate inputs** to prevent security issues
8. **Use caching** for expensive operations

## Common Patterns

### Tool Implementation Pattern

```java
public class MyTool implements Tool {
    @Override
    public String getName() { return "my_tool"; }
    
    @Override
    public String getDescription() { return "Description of what this tool does"; }
    
    @Override
    public Map<String, Object> getInputSchema() {
        // Define JSON schema for input validation
    }
    
    @Override
    public Object execute(Map<String, Object> arguments) throws Exception {
        // Validate inputs
        // Perform operation
        // Return structured result
    }
}
```

### Resource Implementation Pattern

```java
public class MyResource implements Resource {
    @Override
    public String getUri() { return "my://resource/uri"; }
    
    @Override
    public String getName() { return "My Resource"; }
    
    @Override
    public String getDescription() { return "Resource description"; }
    
    @Override
    public String getMimeType() { return "application/json"; }
    
    @Override
    public Object getContent() throws Exception {
        // Fetch and return resource content
    }
}
```

## Troubleshooting

See the main [MCP Server Development Guide](../MCP_SERVER_DEVELOPMENT_GUIDE.md#troubleshooting) for detailed troubleshooting information.

## Contributing

When adding new examples:

1. Follow the existing directory structure
2. Include comprehensive documentation
3. Add unit and integration tests
4. Provide clear setup instructions
5. Include Claude Desktop configuration examples

## Resources

- [MCP Specification](https://spec.modelcontextprotocol.io/)
- [Claude Desktop Documentation](https://claude.ai/docs)
- [Main Development Guide](../MCP_SERVER_DEVELOPMENT_GUIDE.md)
