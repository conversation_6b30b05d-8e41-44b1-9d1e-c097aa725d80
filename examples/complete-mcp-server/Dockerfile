# Multi-stage build for MCP Server
FROM maven:3.9-openjdk-11-slim AS builder

# Set working directory
WORKDIR /app

# Copy pom.xml first for better layer caching
COPY pom.xml .

# Download dependencies
RUN mvn dependency:go-offline -B

# Copy source code
COPY src ./src

# Build the application
RUN mvn clean package -DskipTests

# Runtime stage
FROM openjdk:11-jre-slim

# Install necessary packages
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    curl \
    jq \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd -r mcpuser && useradd -r -g mcpuser mcpuser

# Set working directory
WORKDIR /app

# Create logs directory
RUN mkdir -p logs && chown -R mcpuser:mcpuser logs

# Copy the JAR file from builder stage
COPY --from=builder /app/target/mcp-server-example-*.jar app.jar

# Copy configuration files
COPY --from=builder /app/src/main/resources/logback.xml logback.xml

# Change ownership to app user
RUN chown -R mcpuser:mcpuser /app

# Switch to app user
USER mcpuser

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD echo '{"jsonrpc":"2.0","id":1,"method":"ping"}' | java -jar app.jar || exit 1

# Expose port (if using HTTP transport)
EXPOSE 8080

# Environment variables
ENV LOG_LEVEL=INFO
ENV ROOT_LOG_LEVEL=WARN
ENV JAVA_OPTS="-Xmx512m -Xms256m"

# Run the application
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]

# Labels for metadata
LABEL maintainer="<EMAIL>"
LABEL version="1.0.0"
LABEL description="MCP Server Example - Java Implementation"
LABEL org.opencontainers.image.source="https://github.com/your-org/mcp-server-java"
