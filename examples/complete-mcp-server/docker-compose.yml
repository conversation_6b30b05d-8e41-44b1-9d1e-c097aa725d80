version: '3.8'

services:
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mcp-server-example
    environment:
      - LOG_LEVEL=DEBUG
      - ROOT_LOG_LEVEL=INFO
      - JAVA_OPTS=-Xmx1g -Xms512m
    volumes:
      # Mount logs directory for persistence
      - ./logs:/app/logs
      # Mount data directory if needed
      - ./data:/app/data:ro
    ports:
      # Expose port if using HTTP transport
      - "8080:8080"
    restart: unless-stopped
    networks:
      - mcp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a database for testing
  postgres:
    image: postgres:15-alpine
    container_name: mcp-postgres
    environment:
      POSTGRES_DB: mcptest
      POSTGRES_USER: mcpuser
      POSTGRES_PASSWORD: mcppass
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - mcp-network
    restart: unless-stopped

  # Optional: Redis for caching
  redis:
    image: redis:7-alpine
    container_name: mcp-redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - mcp-network
    restart: unless-stopped

networks:
  mcp-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
