# MCP Server Development Guide

## Table of Contents
1. [Introduction to MCP Architecture](#introduction-to-mcp-architecture)
2. [Setup and Prerequisites](#setup-and-prerequisites)
3. [Implementation Steps](#implementation-steps)
4. [Best Practices](#best-practices)
5. [Testing and Deployment](#testing-and-deployment)
6. [Troubleshooting](#troubleshooting)

## 1. Introduction to MCP Architecture

### What is MCP?

The Model Context Protocol (MCP) is an open standard that enables AI assistants to securely connect to external data sources and tools. MCP servers act as intermediaries that provide structured access to resources, tools, and prompts.

### Core Components

- **Server**: Provides tools, resources, and prompts to clients
- **Client**: Consumes capabilities from MCP servers (e.g., Claude Desktop)
- **Transport**: Communication layer (STDIO, SSE, WebSocket)
- **Protocol**: Standardized message format for client-server communication

### Benefits of Custom MCP Servers

- **Extensibility**: Add custom functionality to AI assistants
- **Security**: Controlled access to sensitive resources
- **Integration**: Connect AI to existing systems and databases
- **Standardization**: Use open protocol for interoperability

## 2. Setup and Prerequisites

### Required Dependencies

```xml
<!-- pom.xml -->
<dependencies>
    <dependency>
        <groupId>org.json</groupId>
        <artifactId>json</artifactId>
        <version>20231013</version>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>2.15.2</version>
    </dependency>
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>2.0.7</version>
    </dependency>
    <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>1.4.11</version>
    </dependency>
</dependencies>
```

### Development Environment

- **Java**: Version 11 or higher
- **Build Tool**: Maven or Gradle
- **IDE**: IntelliJ IDEA, Eclipse, or VS Code
- **Testing**: JUnit 5 for unit tests

## 3. Implementation Steps

### 3.1 Server Initialization

Create the main server class with proper configuration:

```java
package com.example.mcp;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class MCPServer {
    private static final Logger logger = LoggerFactory.getLogger(MCPServer.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Map<String, Tool> tools = new ConcurrentHashMap<>();
    private final Map<String, Resource> resources = new ConcurrentHashMap<>();
    private final Map<String, Prompt> prompts = new ConcurrentHashMap<>();
    
    private String serverName;
    private String serverVersion;
    private boolean initialized = false;
    
    public MCPServer(String name, String version) {
        this.serverName = name;
        this.serverVersion = version;
    }
    
    public void initialize() {
        logger.info("Initializing MCP Server: {} v{}", serverName, serverVersion);
        registerDefaultTools();
        registerDefaultResources();
        registerDefaultPrompts();
        initialized = true;
        logger.info("MCP Server initialized successfully");
    }
    
    private void registerDefaultTools() {
        // Register built-in tools
        addTool(new EchoTool());
        addTool(new FileReadTool());
    }
    
    private void registerDefaultResources() {
        // Register default resources
        addResource(new FileResource());
    }
    
    private void registerDefaultPrompts() {
        // Register default prompts
        addPrompt(new HelpPrompt());
    }
}
```

### 3.2 Adding Tools

Implement the Tool interface and create custom tools:

```java
public interface Tool {
    String getName();
    String getDescription();
    Map<String, Object> getInputSchema();
    Object execute(Map<String, Object> arguments) throws Exception;
}

public class EchoTool implements Tool {
    @Override
    public String getName() {
        return "echo";
    }
    
    @Override
    public String getDescription() {
        return "Echoes back the provided message";
    }
    
    @Override
    public Map<String, Object> getInputSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        
        Map<String, Object> properties = new HashMap<>();
        Map<String, Object> messageProperty = new HashMap<>();
        messageProperty.put("type", "string");
        messageProperty.put("description", "Message to echo back");
        properties.put("message", messageProperty);
        
        schema.put("properties", properties);
        schema.put("required", Arrays.asList("message"));
        
        return schema;
    }
    
    @Override
    public Object execute(Map<String, Object> arguments) throws Exception {
        String message = (String) arguments.get("message");
        if (message == null) {
            throw new IllegalArgumentException("Message parameter is required");
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("content", Arrays.asList(Map.of(
            "type", "text",
            "text", "Echo: " + message
        )));
        
        return result;
    }
}
```

### 3.3 Adding Resources

Implement resources for data access:

```java
public interface Resource {
    String getUri();
    String getName();
    String getDescription();
    String getMimeType();
    Object getContent() throws Exception;
}

public class FileResource implements Resource {
    private final String filePath;
    
    public FileResource(String filePath) {
        this.filePath = filePath;
    }
    
    @Override
    public String getUri() {
        return "file://" + filePath;
    }
    
    @Override
    public String getName() {
        return new File(filePath).getName();
    }
    
    @Override
    public String getDescription() {
        return "File resource: " + filePath;
    }
    
    @Override
    public String getMimeType() {
        return "text/plain";
    }
    
    @Override
    public Object getContent() throws Exception {
        return Files.readString(Paths.get(filePath));
    }
}
```

### 3.4 Adding Prompts

Implement prompts for AI interactions:

```java
public interface Prompt {
    String getName();
    String getDescription();
    Map<String, Object> getArguments();
    List<Map<String, Object>> getMessages(Map<String, Object> arguments);
}

public class HelpPrompt implements Prompt {
    @Override
    public String getName() {
        return "help";
    }

    @Override
    public String getDescription() {
        return "Provides help information about available tools";
    }

    @Override
    public Map<String, Object> getArguments() {
        Map<String, Object> args = new HashMap<>();
        Map<String, Object> properties = new HashMap<>();

        Map<String, Object> topicProperty = new HashMap<>();
        topicProperty.put("type", "string");
        topicProperty.put("description", "Specific topic to get help for");
        properties.put("topic", topicProperty);

        args.put("type", "object");
        args.put("properties", properties);

        return args;
    }

    @Override
    public List<Map<String, Object>> getMessages(Map<String, Object> arguments) {
        String topic = (String) arguments.getOrDefault("topic", "general");

        String helpText = generateHelpText(topic);

        return Arrays.asList(Map.of(
            "role", "user",
            "content", Map.of(
                "type", "text",
                "text", helpText
            )
        ));
    }

    private String generateHelpText(String topic) {
        switch (topic.toLowerCase()) {
            case "tools":
                return "Available tools: echo, file_read. Use tools to perform actions.";
            case "resources":
                return "Resources provide access to data. Use file:// URIs to access files.";
            default:
                return "This MCP server provides tools and resources. Ask for help on 'tools' or 'resources'.";
        }
    }
}
```

### 3.5 Transport Implementation (STDIO)

Implement STDIO transport for communication:

```java
public class STDIOTransport {
    private static final Logger logger = LoggerFactory.getLogger(STDIOTransport.class);
    private final MCPServer server;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final BufferedReader reader;
    private final PrintWriter writer;

    public STDIOTransport(MCPServer server) {
        this.server = server;
        this.reader = new BufferedReader(new InputStreamReader(System.in));
        this.writer = new PrintWriter(System.out, true);
    }

    public void start() {
        logger.info("Starting STDIO transport");

        try {
            String line;
            while ((line = reader.readLine()) != null) {
                processMessage(line);
            }
        } catch (IOException e) {
            logger.error("Error in STDIO transport", e);
        }
    }

    private void processMessage(String message) {
        try {
            Map<String, Object> request = objectMapper.readValue(message, Map.class);
            Map<String, Object> response = handleRequest(request);

            String responseJson = objectMapper.writeValueAsString(response);
            writer.println(responseJson);

        } catch (Exception e) {
            logger.error("Error processing message: {}", message, e);
            sendErrorResponse(e.getMessage());
        }
    }

    private Map<String, Object> handleRequest(Map<String, Object> request) {
        String method = (String) request.get("method");
        Map<String, Object> params = (Map<String, Object>) request.get("params");
        Object id = request.get("id");

        Map<String, Object> response = new HashMap<>();
        response.put("jsonrpc", "2.0");
        response.put("id", id);

        try {
            Object result = dispatchMethod(method, params);
            response.put("result", result);
        } catch (Exception e) {
            Map<String, Object> error = new HashMap<>();
            error.put("code", -1);
            error.put("message", e.getMessage());
            response.put("error", error);
        }

        return response;
    }

    private Object dispatchMethod(String method, Map<String, Object> params) throws Exception {
        switch (method) {
            case "initialize":
                return handleInitialize(params);
            case "tools/list":
                return handleToolsList();
            case "tools/call":
                return handleToolsCall(params);
            case "resources/list":
                return handleResourcesList();
            case "resources/read":
                return handleResourcesRead(params);
            case "prompts/list":
                return handlePromptsList();
            case "prompts/get":
                return handlePromptsGet(params);
            default:
                throw new UnsupportedOperationException("Unknown method: " + method);
        }
    }

    private Map<String, Object> handleInitialize(Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        result.put("protocolVersion", "2024-11-05");
        result.put("serverInfo", Map.of(
            "name", server.getServerName(),
            "version", server.getServerVersion()
        ));
        result.put("capabilities", Map.of(
            "tools", Map.of("listChanged", true),
            "resources", Map.of("subscribe", true, "listChanged", true),
            "prompts", Map.of("listChanged", true)
        ));
        return result;
    }

    private Map<String, Object> handleToolsList() {
        List<Map<String, Object>> toolsList = server.getTools().values().stream()
            .map(tool -> Map.of(
                "name", tool.getName(),
                "description", tool.getDescription(),
                "inputSchema", tool.getInputSchema()
            ))
            .collect(Collectors.toList());

        return Map.of("tools", toolsList);
    }

    private Map<String, Object> handleToolsCall(Map<String, Object> params) throws Exception {
        String name = (String) params.get("name");
        Map<String, Object> arguments = (Map<String, Object>) params.get("arguments");

        Tool tool = server.getTool(name);
        if (tool == null) {
            throw new IllegalArgumentException("Tool not found: " + name);
        }

        return (Map<String, Object>) tool.execute(arguments);
    }

    private Map<String, Object> handleResourcesList() {
        List<Map<String, Object>> resourcesList = server.getResources().values().stream()
            .map(resource -> Map.of(
                "uri", resource.getUri(),
                "name", resource.getName(),
                "description", resource.getDescription(),
                "mimeType", resource.getMimeType()
            ))
            .collect(Collectors.toList());

        return Map.of("resources", resourcesList);
    }

    private Map<String, Object> handleResourcesRead(Map<String, Object> params) throws Exception {
        String uri = (String) params.get("uri");

        Resource resource = server.getResourceByUri(uri);
        if (resource == null) {
            throw new IllegalArgumentException("Resource not found: " + uri);
        }

        return Map.of(
            "contents", Arrays.asList(Map.of(
                "uri", resource.getUri(),
                "mimeType", resource.getMimeType(),
                "text", resource.getContent()
            ))
        );
    }

    private Map<String, Object> handlePromptsList() {
        List<Map<String, Object>> promptsList = server.getPrompts().values().stream()
            .map(prompt -> Map.of(
                "name", prompt.getName(),
                "description", prompt.getDescription(),
                "arguments", prompt.getArguments()
            ))
            .collect(Collectors.toList());

        return Map.of("prompts", promptsList);
    }

    private Map<String, Object> handlePromptsGet(Map<String, Object> params) throws Exception {
        String name = (String) params.get("name");
        Map<String, Object> arguments = (Map<String, Object>) params.getOrDefault("arguments", new HashMap<>());

        Prompt prompt = server.getPrompt(name);
        if (prompt == null) {
            throw new IllegalArgumentException("Prompt not found: " + name);
        }

        return Map.of(
            "description", prompt.getDescription(),
            "messages", prompt.getMessages(arguments)
        );
    }

    private void sendErrorResponse(String message) {
        Map<String, Object> error = Map.of(
            "jsonrpc", "2.0",
            "error", Map.of(
                "code", -32603,
                "message", message
            )
        );

        try {
            String errorJson = objectMapper.writeValueAsString(error);
            writer.println(errorJson);
        } catch (Exception e) {
            logger.error("Failed to send error response", e);
        }
    }
}
```

### 3.6 Main Application Class

Create the main class to run the server:

```java
public class MCPServerApplication {
    private static final Logger logger = LoggerFactory.getLogger(MCPServerApplication.class);

    public static void main(String[] args) {
        try {
            // Create and initialize the MCP server
            MCPServer server = new MCPServer("example-mcp-server", "1.0.0");
            server.initialize();

            // Start STDIO transport
            STDIOTransport transport = new STDIOTransport(server);
            transport.start();

        } catch (Exception e) {
            logger.error("Failed to start MCP server", e);
            System.exit(1);
        }
    }
}
```

## 4. Best Practices

### 4.1 Error Handling and Logging

Implement comprehensive error handling:

```java
public class MCPErrorHandler {
    private static final Logger logger = LoggerFactory.getLogger(MCPErrorHandler.class);

    public static Map<String, Object> createErrorResponse(Object id, int code, String message) {
        return createErrorResponse(id, code, message, null);
    }

    public static Map<String, Object> createErrorResponse(Object id, int code, String message, Object data) {
        Map<String, Object> error = new HashMap<>();
        error.put("code", code);
        error.put("message", message);

        if (data != null) {
            error.put("data", data);
        }

        Map<String, Object> response = new HashMap<>();
        response.put("jsonrpc", "2.0");
        response.put("id", id);
        response.put("error", error);

        logger.error("MCP Error [{}]: {}", code, message);

        return response;
    }

    public static class MCPException extends Exception {
        private final int code;
        private final Object data;

        public MCPException(int code, String message) {
            this(code, message, null);
        }

        public MCPException(int code, String message, Object data) {
            super(message);
            this.code = code;
            this.data = data;
        }

        public int getCode() { return code; }
        public Object getData() { return data; }
    }
}
```

### 4.2 Performance Optimization

Implement caching and connection pooling:

```java
public class MCPCache {
    private final Map<String, Object> cache = new ConcurrentHashMap<>();
    private final long ttlMillis;
    private final Map<String, Long> timestamps = new ConcurrentHashMap<>();

    public MCPCache(long ttlMillis) {
        this.ttlMillis = ttlMillis;
    }

    public void put(String key, Object value) {
        cache.put(key, value);
        timestamps.put(key, System.currentTimeMillis());
    }

    public Object get(String key) {
        Long timestamp = timestamps.get(key);
        if (timestamp == null) {
            return null;
        }

        if (System.currentTimeMillis() - timestamp > ttlMillis) {
            cache.remove(key);
            timestamps.remove(key);
            return null;
        }

        return cache.get(key);
    }

    public void invalidate(String key) {
        cache.remove(key);
        timestamps.remove(key);
    }

    public void clear() {
        cache.clear();
        timestamps.clear();
    }
}
```

### 4.3 Security Considerations

Implement input validation and sanitization:

```java
public class MCPValidator {
    private static final int MAX_STRING_LENGTH = 10000;
    private static final int MAX_ARRAY_SIZE = 1000;

    public static void validateToolArguments(Map<String, Object> arguments) throws MCPException {
        if (arguments == null) {
            return;
        }

        validateObject(arguments, 0);
    }

    private static void validateObject(Object obj, int depth) throws MCPException {
        if (depth > 10) {
            throw new MCPException(-32602, "Object nesting too deep");
        }

        if (obj instanceof String) {
            String str = (String) obj;
            if (str.length() > MAX_STRING_LENGTH) {
                throw new MCPException(-32602, "String too long");
            }
        } else if (obj instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) obj;
            if (map.size() > MAX_ARRAY_SIZE) {
                throw new MCPException(-32602, "Object has too many properties");
            }

            for (Object value : map.values()) {
                validateObject(value, depth + 1);
            }
        } else if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            if (list.size() > MAX_ARRAY_SIZE) {
                throw new MCPException(-32602, "Array too large");
            }

            for (Object item : list) {
                validateObject(item, depth + 1);
            }
        }
    }

    public static String sanitizeString(String input) {
        if (input == null) {
            return null;
        }

        // Remove potentially dangerous characters
        return input.replaceAll("[<>\"'&]", "")
                   .trim();
    }
}
```

## 5. Testing and Deployment

### 5.1 Unit Testing

Create comprehensive unit tests:

```java
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

public class MCPServerTest {
    private MCPServer server;

    @BeforeEach
    void setUp() {
        server = new MCPServer("test-server", "1.0.0");
        server.initialize();
    }

    @Test
    void testEchoTool() throws Exception {
        Tool echoTool = server.getTool("echo");
        assertNotNull(echoTool);

        Map<String, Object> arguments = Map.of("message", "Hello, World!");
        Map<String, Object> result = (Map<String, Object>) echoTool.execute(arguments);

        assertNotNull(result);
        assertTrue(result.containsKey("content"));
    }

    @Test
    void testToolNotFound() {
        Tool nonExistentTool = server.getTool("nonexistent");
        assertNull(nonExistentTool);
    }

    @Test
    void testResourceAccess() throws Exception {
        // Create a temporary file for testing
        Path tempFile = Files.createTempFile("test", ".txt");
        Files.write(tempFile, "Test content".getBytes());

        FileResource resource = new FileResource(tempFile.toString());
        assertEquals("Test content", resource.getContent());

        // Clean up
        Files.delete(tempFile);
    }
}
```

### 5.2 Integration Testing

Test the complete MCP protocol flow:

```java
public class MCPIntegrationTest {
    private MCPServer server;
    private STDIOTransport transport;

    @BeforeEach
    void setUp() {
        server = new MCPServer("integration-test-server", "1.0.0");
        server.initialize();
    }

    @Test
    void testInitializeProtocol() throws Exception {
        Map<String, Object> initRequest = Map.of(
            "jsonrpc", "2.0",
            "id", 1,
            "method", "initialize",
            "params", Map.of(
                "protocolVersion", "2024-11-05",
                "capabilities", Map.of(),
                "clientInfo", Map.of(
                    "name", "test-client",
                    "version", "1.0.0"
                )
            )
        );

        // Test initialization response
        // Implementation would depend on your testing framework
    }

    @Test
    void testToolsListAndCall() throws Exception {
        // Test tools/list
        Map<String, Object> listRequest = Map.of(
            "jsonrpc", "2.0",
            "id", 2,
            "method", "tools/list"
        );

        // Test tools/call
        Map<String, Object> callRequest = Map.of(
            "jsonrpc", "2.0",
            "id", 3,
            "method", "tools/call",
            "params", Map.of(
                "name", "echo",
                "arguments", Map.of("message", "test")
            )
        );

        // Verify responses
    }
}
```

### 5.3 Claude Desktop Integration

Configure your MCP server with Claude Desktop by adding to the configuration file:

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "example-mcp-server": {
      "command": "java",
      "args": [
        "-jar",
        "/path/to/your/mcp-server.jar"
      ],
      "env": {
        "LOG_LEVEL": "INFO"
      }
    }
  }
}
```

### 5.4 Deployment Options

#### Option 1: Standalone JAR

Create a fat JAR with all dependencies:

```xml
<!-- Add to pom.xml -->
<build>
    <plugins>
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-shade-plugin</artifactId>
            <version>3.4.1</version>
            <executions>
                <execution>
                    <phase>package</phase>
                    <goals>
                        <goal>shade</goal>
                    </goals>
                    <configuration>
                        <transformers>
                            <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                <mainClass>com.example.mcp.MCPServerApplication</mainClass>
                            </transformer>
                        </transformers>
                    </configuration>
                </execution>
            </executions>
        </plugin>
    </plugins>
</build>
```

#### Option 2: Docker Container

```dockerfile
FROM openjdk:11-jre-slim

WORKDIR /app
COPY target/mcp-server.jar app.jar

EXPOSE 8080

CMD ["java", "-jar", "app.jar"]
```

#### Option 3: Native Binary (GraalVM)

```xml
<plugin>
    <groupId>org.graalvm.buildtools</groupId>
    <artifactId>native-maven-plugin</artifactId>
    <version>0.9.27</version>
    <executions>
        <execution>
            <id>build-native</id>
            <goals>
                <goal>compile-no-fork</goal>
            </goals>
            <phase>package</phase>
        </execution>
    </executions>
</plugin>
```

## 6. Troubleshooting

### Common Issues and Solutions

#### 6.1 Connection Issues

**Problem**: Client cannot connect to MCP server
**Solutions**:
- Verify STDIO transport is properly configured
- Check that the server executable is in the correct path
- Ensure proper permissions on the executable file
- Validate JSON-RPC message format

#### 6.2 Tool Execution Errors

**Problem**: Tools fail to execute or return errors
**Solutions**:
- Validate input schema matches the provided arguments
- Check for proper error handling in tool implementation
- Ensure all required dependencies are available
- Add comprehensive logging to identify the root cause

#### 6.3 Resource Access Issues

**Problem**: Resources cannot be read or accessed
**Solutions**:
- Verify file paths and permissions
- Check URI format and encoding
- Implement proper error handling for missing resources
- Validate MIME type detection

#### 6.4 Performance Problems

**Problem**: Server responds slowly or times out
**Solutions**:
- Implement caching for frequently accessed resources
- Use connection pooling for database access
- Add request timeout handling
- Profile and optimize resource-intensive operations

#### 6.5 Memory Issues

**Problem**: Server consumes excessive memory
**Solutions**:
- Implement proper resource cleanup
- Use streaming for large data transfers
- Add memory limits and monitoring
- Optimize data structures and algorithms

### Debugging Tips

1. **Enable Debug Logging**:
```java
// Add to logback.xml
<logger name="com.example.mcp" level="DEBUG"/>
```

2. **Use MCP Inspector**: Test your server with the official MCP inspector tool

3. **Validate JSON-RPC**: Ensure all messages follow the JSON-RPC 2.0 specification

4. **Test Incrementally**: Start with basic functionality and add features gradually

5. **Monitor Resource Usage**: Use profiling tools to identify bottlenecks

### Error Codes Reference

| Code | Description | Common Causes |
|------|-------------|---------------|
| -32700 | Parse error | Invalid JSON format |
| -32600 | Invalid Request | Missing required fields |
| -32601 | Method not found | Unsupported MCP method |
| -32602 | Invalid params | Wrong parameter types |
| -32603 | Internal error | Server-side exceptions |

## Conclusion

This guide provides a comprehensive foundation for building robust MCP servers in Java. Key takeaways:

1. **Start Simple**: Begin with basic tools and resources, then expand functionality
2. **Follow Standards**: Adhere to the MCP protocol specification
3. **Test Thoroughly**: Implement comprehensive unit and integration tests
4. **Handle Errors Gracefully**: Provide meaningful error messages and proper error codes
5. **Optimize Performance**: Use caching and efficient data structures
6. **Secure by Design**: Validate inputs and sanitize outputs
7. **Monitor and Log**: Implement comprehensive logging for debugging

For more information, refer to the official MCP documentation and community resources.

---

*This guide covers MCP protocol version 2024-11-05. Check for updates to the protocol specification.*
